package org.cdzg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.TypeReference;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.Set;

/**
 * 优惠券领取异常场景
 *
 * <AUTHOR>
 * @since 2025/4/23 11:05
 */
@Getter
@AllArgsConstructor
public enum CouponReceiveExceptionSceneEnum {

    NOT_ACCORD_CONDITION("notCondition", "不满足领取条件", Set.of("黑名单校验不通过", "使用后领取规则未通过", "展示规则未通过")),
    REPEAT_RECEIVE("repeatReceive", "请勿重复领取", Set.of("幂等", "领取规则未通过")),
    NOT_SUPPORTED_RECEIVE("notSupportedReceive", "暂不支持领取该优惠券", Set.of("活动类型为对外优惠券活动或外部领券活动")),
    SYSTEM_BUSY("systemBusy", "活动太火爆了，请稍后重试", Set.of("用户锁获取失败", "库存锁获取失败")),
    NOT_ACCORD_RULE("notAccordRule", "该优惠券不满足领取条件，请领取其他优惠券", Set.of("不满足规则引擎")),
    ;

    /**
     * 配置中心对象key
     */
    private final String key;

    /**
     * 默认提示
     */
    private final String tip;

    /**
     * 优惠券领取时，异常提示的场景
     */
    private final Set<String> scenes;

    /**
     * 根据配置获取提示语
     *
     * @return
     */
    public String getTipByConfig(String config) {
        config = "{\n" +
                "\t\"notCondition1\": \"不满足领取条件1\",\n" +
                "\t\"repeatReceive\": \"重复领取1\",\n" +
                "\t\"notSupportedReceive\": \"不支持领取1\",\n" +
                "\t\"systemBusy\": \"活动太火爆了，请稍后重试1\",\n" +
                "\t\"notAccordRule\": \"不符合规则1\"\n" +
                "}";
        String tip;
        try {
            Map<String, String> map = JSON.parseObject(config, Map.class);
            tip = map.get(this.key) == null ? this.tip : map.get(this.key);
        } catch (JSONException e) {
            tip = this.tip;
        }
        return tip;
    }

}
