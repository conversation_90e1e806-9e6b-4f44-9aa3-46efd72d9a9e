package org.cdzg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 支付页配置
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PaymentPageConfig implements Serializable {
  private static final long serialVersionUID = -93857642953078108L;
  /**
   * 主键
   */
  private Long id;
  /**
   * 申请时间
   */
  private Date createTime;
  /**
   * 创建人id
   */
  private String createBy;
  /**
   * 更新时间
   */
  private Date updateTime;
  /**
   * 更新人id
   */
  private String updateBy;
  /**
   * 是否删除（0否，1是）
   */
  private Boolean del;

  /**
   * 支付渠道
   */
  private Integer paySrc;

  /**
   * 配置类型
   */
  private Integer configType;

  /**
   * 引用对应id
   */
  private String targetId;

  /**
   * 类目id
   */
  private String categoryId;

  /**
   * 类目名字
   */
  private String categoryName;

  /**
   * 分组id
   */
  private String extId;

  /**
   * 备注
   */
  private String remark;

  /**
   * 商家id
   */
  private String merchantId;

  /**
   * 商家名称
   */
  private String merchantName;



}
