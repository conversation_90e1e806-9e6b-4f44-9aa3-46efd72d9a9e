package org.cdzg;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

public class Main {

    // 优惠券缓存key前缀
    private static final String COUPON_STOCK_CACHE_KEY_PREFIX = "coupon:activity:stock:";

    // 优惠券缓存key分隔符
    private static final String SEPARATOR = ":";

    private static final DateTimeFormatter INPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    public static void main(String[] args)  {
        Date now = new Date();
        Date start = DateUtil.beginOfDay(now);
        Date end = DateUtil.endOfDay(now);
        System.out.println(start);
        System.out.println(end);
    }


    private static Integer ccc(int i){
        switch (i){
            case 0:
                break;
            case 1:
                return 1;
            default:
                break;
        }
        return 2;
    }
    public static  boolean areIntervalsNonOverlapping(List<MarketingModuleRunOutImgUrlRequest> intervals) {
        // 空列表或单元素列表默认不交叉
        if (intervals == null || intervals.size() <= 1) {
            return true;
        }

        // 按开始时间升序排序
        intervals.sort(Comparator.comparing(MarketingModuleRunOutImgUrlRequest::getStartTime));

        // 检查相邻时间段是否交叉
        for (int i = 0; i < intervals.size() - 1; i++) {
            Date currentEnd = intervals.get(i).getEndTime();
            Date nextStart = intervals.get(i + 1).getStartTime();

            // 当前结束时间 > 下一个开始时间 表示有交叉
            if (currentEnd.after(nextStart)) {
                return false;
            }
        }

        return true;
    }

    private static void heapOverFlow() {
        List<Byte[]> list = new ArrayList<>();
        Byte[] bytes = new Byte[1024 * 1024];
        while (true) {
            list.add(bytes);
        }
    }

    private static void digui(int i) {
        System.out.println("栈深度：" + i);
        digui(i + 1);
    }

    /**
     * 计算小时差
     * 当前时间>startTime,开始时间则取当前时间
     *
     * @param startTime 活动开始时间
     * @param endTime   活动结束时间
     */
    private static long computeTTL(Date startTime, Date endTime) {
        if (startTime.getTime() > System.currentTimeMillis()) {
            startTime = new Date();
        }
        long startMillis = startTime.getTime();
        long endMillis = endTime.getTime();
        long diffMillis = endMillis - startMillis;

        return diffMillis / (60 * 1000);
    }


    private static Map<String, Date> getLaunchTimeMap(List<String> keys, Long activityId, Long couponId) {

        String prefix = COUPON_STOCK_CACHE_KEY_PREFIX + activityId + SEPARATOR + couponId + SEPARATOR;
        Map<String, Date> launchTimeMap = new HashMap<>();
        for (String key : keys) {
            if (key.startsWith(prefix)) {
                String launchTime = key.substring(prefix.length());
                LocalDateTime localDateTime = LocalDateTime.parse(launchTime, OUTPUT_FORMATTER);
                launchTimeMap.put(key, Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()));
            }
        }
        return launchTimeMap;
    }






    /**
     * 递归处理树节点
     *
     * @param roleIds          当前节点
     * @param roleTreeValidVos 父节点
     */
    public static List<AdminRoleTreeValidity> parseRoleTree(List<Long> roleIds, List<RoleTreeValidityVo> roleTreeValidVos) {
        // 1. 将角色ID列表转换为Set提高查询效率
        Set<Long> roleIdSet = new HashSet<>(roleIds);

        // 2. 存储最终结果的列表
        List<AdminRoleTreeValidity> resultList = new ArrayList<>();

        // 3. 递归处理每个树节点
        for (RoleTreeValidityVo root : roleTreeValidVos) {
            processTreeNode(root, null, roleIdSet, resultList);
        }
        return resultList;
    }

    /**
     * 递归处理树节点
     *
     * @param node       当前节点
     * @param parentPath 父节点路径（格式：id1.id2）
     * @param roleIdSet  勾选的角色ID集合
     * @param resultList 结果集合
     * @return 返回当前节点是否被勾选
     */
    private static boolean processTreeNode(RoleTreeValidityVo node, String parentPath,
                                    Set<Long> roleIdSet, List<AdminRoleTreeValidity> resultList) {
        // 1. 构建当前节点路径
        String currentPath = (parentPath == null) ? String.valueOf(node.getId()) : parentPath + "." + node.getId();

        // 2. 处理叶子节点（角色节点）
        if (isLeaf(node)) {
            if (roleIdSet.contains(node.getId())) {
                // 创建有效记录
                resultList.add(buildAdminRoleTreeValidity(currentPath, node.getStartTime(), node.getEndTime()));
                // 标记为勾选
                return true;
            }
            // 未勾选
            return false;
        }
        // 3. 处理非叶子节点（目录节点）
        boolean allChildrenChecked = true;
        boolean anyChildChecked = false;

        for (RoleTreeValidityVo child : node.getChildren()) {
            boolean childChecked = processTreeNode(child, currentPath, roleIdSet, resultList);
            if (!childChecked) {
                allChildrenChecked = false; // 存在未勾选的子节点
            } else {
                anyChildChecked = true; // 存在至少一个勾选的子节点
            }
        }
        // 4. 根据子节点勾选状态处理当前节点
        if (allChildrenChecked) {
            // 所有子节点勾选 -> 当前节点自动勾选
            resultList.add(buildAdminRoleTreeValidity(currentPath, node.getStartTime(), node.getEndTime()));
            return true;
        } else if (anyChildChecked) {
            // 部分子节点勾选 -> 仅添加当前节点（非完整勾选）
            resultList.add(buildAdminRoleTreeValidity(currentPath, node.getStartTime(), node.getEndTime()));
            return false; // 非完整勾选状态
        }
        return false; // 没有子节点被勾选
    }

    /**
     * 判断是否是叶子节点
     */
    private static boolean isLeaf(RoleTreeValidityVo node) {
        return node.getChildren() == null || node.getChildren().isEmpty();
    }

    /**
     * 创建入库实体对象
     */
    private static AdminRoleTreeValidity buildAdminRoleTreeValidity(String path, Date startTime, Date endTime) {
        AdminRoleTreeValidity item = new AdminRoleTreeValidity();
        item.setRoleTreePath(path);
        // 可能为null（永久有效）
        item.setStartTime(startTime);
        item.setEndTime(endTime);
        return item;
    }



    public static List<RoleTreeValidityVo> convertToFullTree(
            List<RoleTreeValidityVo> fullTree,
            List<AdminRoleTreeValidity> checkedList
    ) {
        // 1. 将勾选列表转换为路径到有效期信息的映射
        Map<String, ValidityInfo> validityMap = new HashMap<>();
        for (AdminRoleTreeValidity item : checkedList) {
            validityMap.put(item.getRoleTreePath(),
                    new ValidityInfo(item.getStartTime(), item.getEndTime()));
        }

        // 2. 递归处理整棵树
        List<RoleTreeValidityVo> resultTree = new ArrayList<>();
        for (RoleTreeValidityVo root : fullTree) {
            resultTree.add(processTreeNode(root, null, validityMap));
        }

        return resultTree;
    }

    private static RoleTreeValidityVo processTreeNode(
            RoleTreeValidityVo node,
            String parentPath,
            Map<String, ValidityInfo> validityMap
    ) {
        // 1. 创建当前节点的副本
        RoleTreeValidityVo newNode = new RoleTreeValidityVo();
        newNode.setId(node.getId());
        newNode.setName(node.getName());

        // 2. 构建当前节点路径
        String currentPath = (parentPath == null)
                ? String.valueOf(node.getId())
                : parentPath + "." + node.getId();

        // 3. 设置有效期（如果被勾选）
        ValidityInfo validity = validityMap.get(currentPath);
        if (validity != null) {
            newNode.setStartTime(validity.startTime);
            newNode.setEndTime(validity.endTime);
        }

        // 4. 递归处理子节点
        List<RoleTreeValidityVo> newChildren = new ArrayList<>();
        for (RoleTreeValidityVo child : node.getChildren()) {
            newChildren.add(processTreeNode(child, currentPath, validityMap));
        }
        newNode.setChildren(newChildren);

        return newNode;
    }

    // 辅助类用于存储有效期信息
    private static class ValidityInfo {
        Date startTime;
        Date endTime;

        ValidityInfo(Date startTime, Date endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }
    }































    // 过期提醒阈值（7天）
    private static final long EXPIRATION_THRESHOLD = 7 * 24 * 60 * 60 * 1000L;

    /**
     * 检查即将过期的角色
     * @param fullTree 全量树结构
     * @param validityList 有效期设置列表
     * @return Map<AdminRoleTreeValidity-id, AdminRoleTreeValidity> 需要提醒的角色ID和过期时间
     */
    public Map<Long, AdminRoleTreeValidity> getExpiringRoles(
            List<RoleModuleTree> fullTree,
            List<AdminRoleTreeValidity> validityList
    ) {
        // 1. 构建路径到有效期的映射
        Map<String, AdminRoleTreeValidity> validityMap = validityList.stream()
                .collect(Collectors.toMap(AdminRoleTreeValidity::getRoleTreePath, item -> item));

        // 2. 存储结果：AdminRoleTreeValidity-id -> AdminRoleTreeValidity
        Map<Long, AdminRoleTreeValidity> expiringRoles = new HashMap<>();

        // 3. 递归遍历树结构
        for (RoleModuleTree root : fullTree) {
            traverseTree(root, null, validityMap, expiringRoles);
        }

        return expiringRoles;
    }

    /**
     * 递归遍历树节点
     * @param node 当前节点
     * @param parentPath 父节点路径
     * @param validityMap 有效期映射表
     * @param expiringRoles 结果集合
     */
    private void traverseTree(
            RoleModuleTree node,
            String parentPath,
            Map<String, AdminRoleTreeValidity> validityMap,
            Map<Long, AdminRoleTreeValidity> expiringRoles
    ) {
        // 构建当前节点路径
        String currentPath = (parentPath == null)
                ? String.valueOf(node.getId())
                : parentPath + "." + node.getId();

        // 如果是叶子节点（角色节点）
        if (isLeaf(node)) {
            // 解析最终有效期
            AdminRoleTreeValidity validity = resolveLeafExpiration(currentPath, validityMap);

            // 检查是否需要提醒
            if (validity != null && isExpiringSoon(validity.getEndTime())) {
                expiringRoles.put(node.getId(), validity);
            }
            return;
        }

        // 递归处理子节点
        for (RoleModuleTree child : node.getChildren()) {
            traverseTree(child, currentPath, validityMap, expiringRoles);
        }
    }

    /**
     * 解析叶子节点的过期时间
     */
    private AdminRoleTreeValidity resolveLeafExpiration(String leafPath, Map<String, AdminRoleTreeValidity> validityMap) {
        // 1. 检查叶子节点自身是否设置了有效期
        AdminRoleTreeValidity leafValidity = validityMap.get(leafPath);
        if (leafValidity != null && leafValidity.getEndTime() != null) {
            return leafValidity;
        }

        // 2. 向上查找父节点的有效期
        String parentPath = leafPath;
        while (parentPath.contains(".")) {
            parentPath = parentPath.substring(0, parentPath.lastIndexOf('.'));
            AdminRoleTreeValidity parentValidity = validityMap.get(parentPath);

            if (parentValidity != null && parentValidity.getEndTime() != null) {
                return parentValidity;
            }
        }

        // 3. 没有找到任何有效期信息
        return null;
    }

    /**
     * 检查是否即将过期（7天内）
     */
    private boolean isExpiringSoon(Date endTime) {
        Date now = new Date();
        if (endTime.after(now)) {
            long diff = endTime.getTime() - now.getTime();
            return diff <= EXPIRATION_THRESHOLD;
        }
        // 已经过期
        return true;
    }

    /**
     * 判断是否是叶子节点
     */
    private boolean isLeaf(RoleModuleTree node) {
        return node.getChildren() == null || node.getChildren().isEmpty();
    }

}