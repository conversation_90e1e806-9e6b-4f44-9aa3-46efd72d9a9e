package org.cdzg;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;

/**
 * 营销模块已抢光图片
 *
 * <AUTHOR>
 * @since 2025/8/4 14:34
 */
@Data
@AllArgsConstructor
public class MarketingModuleRunOutImgUrlRequest {

    /**
     * 已抢光底片
     */
    private String url;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
