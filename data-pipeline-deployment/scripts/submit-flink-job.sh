#!/bin/bash

# Flink CDC作业提交脚本
echo "=== 提交Flink CDC作业 ==="

# 等待Flink完全启动
echo "1. 等待Flink JobManager启动..."
for i in {1..60}; do
    if curl -s http://localhost:8081/overview >/dev/null 2>&1; then
        echo "✓ Flink JobManager已就绪"
        break
    fi
    echo "等待中... ($i/60)"
    sleep 5
done

# 检查TaskManager
echo "2. 检查TaskManager状态..."
TASKMANAGERS=$(curl -s http://localhost:8081/taskmanagers | jq '.taskmanagers | length' 2>/dev/null || echo "0")
if [ "$TASKMANAGERS" -gt 0 ]; then
    echo "✓ 发现 $TASKMANAGERS 个TaskManager"
else
    echo "⚠ 未发现TaskManager，请检查配置"
fi

# 进入Flink容器并启动SQL Client
echo "3. 启动Flink SQL Client..."
docker exec -it flink-jobmanager bash -c "
echo '开始配置Flink CDC作业...'

# 启动SQL Client并执行配置
/opt/flink/bin/sql-client.sh embedded << 'EOF'

-- 设置执行环境
SET execution.checkpointing.interval = 60s;
SET execution.checkpointing.timeout = 10min;
SET execution.checkpointing.min-pause = 5s;

-- 创建MySQL CDC源表 - users
CREATE TABLE mysql_users (
    id INT,
    name STRING,
    email STRING,
    created_at TIMESTAMP(3),
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'mysql-cdc',
    'hostname' = 'mysql-source',
    'port' = '3306',
    'username' = 'flink_user',
    'password' = 'flink123',
    'database-name' = 'test_db',
    'table-name' = 'users',
    'server-time-zone' = 'Asia/Shanghai'
);

-- 创建Paimon目标表 - users
CREATE TABLE paimon_users (
    id INT,
    name STRING,
    email STRING,
    created_at TIMESTAMP(3),
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'paimon',
    'path' = 's3://paimon-warehouse/users',
    's3.endpoint' = 'http://minio-storage:9000',
    's3.access-key' = 'minioadmin',
    's3.secret-key' = 'minioadmin123',
    's3.path-style-access' = 'true'
);

-- 显示表
SHOW TABLES;

-- 提交CDC作业
INSERT INTO paimon_users
SELECT id, name, email, created_at
FROM mysql_users;

EOF
"

echo "4. 检查作业状态..."
sleep 10
JOBS=$(curl -s http://localhost:8081/jobs | jq '.jobs | length' 2>/dev/null || echo "0")
echo "当前运行的作业数: $JOBS"

echo "=== Flink作业配置完成 ==="
echo "访问 http://localhost:8081 查看作业状态"
