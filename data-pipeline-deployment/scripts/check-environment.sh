#!/bin/bash

# 环境检查脚本
echo "=== 数据通路部署环境检查 ==="

# 检查操作系统
echo "1. 检查操作系统..."
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "✓ Linux系统检测通过"
else
    echo "✗ 警告：建议在Linux系统上运行"
fi

# 检查Docker
echo "2. 检查Docker..."
if command -v docker &> /dev/null; then
    DOCKER_VERSION=$(docker --version)
    echo "✓ Docker已安装: $DOCKER_VERSION"
    
    # 检查Docker服务状态
    if docker info &> /dev/null; then
        echo "✓ Docker服务运行正常"
    else
        echo "✗ Docker服务未运行，请启动Docker服务"
        exit 1
    fi
else
    echo "✗ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker Compose
echo "3. 检查Docker Compose..."
if command -v docker-compose &> /dev/null; then
    COMPOSE_VERSION=$(docker-compose --version)
    echo "✓ Docker Compose已安装: $COMPOSE_VERSION"
elif docker compose version &> /dev/null; then
    COMPOSE_VERSION=$(docker compose version)
    echo "✓ Docker Compose (插件版本)已安装: $COMPOSE_VERSION"
else
    echo "✗ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查系统资源
echo "4. 检查系统资源..."

# 检查内存
TOTAL_MEM=$(free -m | awk 'NR==2{printf "%.1f", $2/1024}')
if (( $(echo "$TOTAL_MEM >= 8.0" | bc -l) )); then
    echo "✓ 内存充足: ${TOTAL_MEM}GB"
else
    echo "⚠ 内存可能不足: ${TOTAL_MEM}GB (推荐8GB+)"
fi

# 检查磁盘空间
AVAILABLE_SPACE=$(df -h . | awk 'NR==2 {print $4}')
echo "✓ 可用磁盘空间: $AVAILABLE_SPACE"

# 检查CPU核心数
CPU_CORES=$(nproc)
if [ $CPU_CORES -ge 4 ]; then
    echo "✓ CPU核心数充足: ${CPU_CORES}核"
else
    echo "⚠ CPU核心数较少: ${CPU_CORES}核 (推荐4核+)"
fi

# 检查端口占用
echo "5. 检查端口占用..."
PORTS=(3306 8081 8082 9000 9001 8030 9030 8040 9060)
for port in "${PORTS[@]}"; do
    if netstat -tuln 2>/dev/null | grep -q ":$port "; then
        echo "⚠ 端口 $port 已被占用"
    else
        echo "✓ 端口 $port 可用"
    fi
done

# 创建必要的目录
echo "6. 创建必要的目录..."
mkdir -p libs/flink-cdc
echo "✓ 目录结构创建完成"

echo ""
echo "=== 环境检查完成 ==="
echo "如果所有检查都通过，可以运行: docker-compose up -d"
