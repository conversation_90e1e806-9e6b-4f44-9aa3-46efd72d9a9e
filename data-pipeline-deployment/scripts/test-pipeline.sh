#!/bin/bash

# 数据通路测试脚本
echo "=== 数据通路端到端测试 ==="

# 等待服务启动
echo "1. 等待服务完全启动..."
sleep 30

# 检查MySQL连接
echo "2. 测试MySQL连接..."
docker exec mysql-source mysql -uroot -proot123 -e "SELECT 'MySQL连接成功' as status;"

# 检查Flink JobManager
echo "3. 检查Flink JobManager状态..."
curl -s http://localhost:8081/overview | grep -q "running" && echo "✓ Flink JobManager运行正常" || echo "✗ Flink JobManager异常"

# 检查MinIO
echo "4. 检查MinIO状态..."
curl -s http://localhost:9000/minio/health/live && echo "✓ MinIO运行正常" || echo "✗ MinIO异常"

# 检查Doris FE
echo "5. 检查Doris FE状态..."
curl -s http://localhost:8030/api/bootstrap && echo "✓ Doris FE运行正常" || echo "✗ Doris FE异常"

# 在MySQL中插入测试数据
echo "6. 在MySQL中插入测试数据..."
docker exec mysql-source mysql -uroot -proot123 test_db -e "
INSERT INTO users (name, email) VALUES ('TestUser1', '<EMAIL>');
INSERT INTO products (product_name, price, stock_quantity) VALUES ('TestProduct', 99.99, 100);
"

echo "7. 查看MySQL binlog状态..."
docker exec mysql-source mysql -uroot -proot123 -e "SHOW MASTER STATUS;"

# 创建MinIO bucket
echo "8. 创建MinIO存储桶..."
docker exec minio-storage mc alias set local http://localhost:9000 minioadmin minioadmin123
docker exec minio-storage mc mb local/paimon-warehouse --ignore-existing

echo "9. 测试数据插入完成"
echo "可以通过以下方式监控数据流："
echo "- Flink Web UI: http://localhost:8081"
echo "- MinIO Console: http://localhost:9001 (minioadmin/minioadmin123)"
echo "- Doris FE UI: http://localhost:8030"

echo ""
echo "=== 测试完成 ==="
