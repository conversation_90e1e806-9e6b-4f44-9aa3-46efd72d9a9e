#!/bin/bash

# 下载依赖包脚本
echo "=== 下载Flink CDC和相关依赖 ==="

# 创建libs目录
mkdir -p libs/flink-cdc

# Flink CDC版本
FLINK_CDC_VERSION="2.4.2"
FLINK_VERSION="1.18"

echo "1. 下载Flink CDC MySQL连接器..."
wget -O libs/flink-cdc/flink-cdc-mysql-${FLINK_CDC_VERSION}.jar \
    "https://repo1.maven.org/maven2/com/ververica/flink-cdc-mysql/${FLINK_CDC_VERSION}/flink-cdc-mysql-${FLINK_CDC_VERSION}.jar"

echo "2. 下载Flink Paimon连接器..."
wget -O libs/flink-cdc/paimon-flink-${FLINK_VERSION}-0.6.0.jar \
    "https://repo1.maven.org/maven2/org/apache/paimon/paimon-flink-${FLINK_VERSION}/0.6.0/paimon-flink-${FLINK_VERSION}-0.6.0.jar"

echo "3. 下载Hadoop客户端..."
wget -O libs/flink-cdc/flink-shaded-hadoop-2-uber-2.8.3-10.0.jar \
    "https://repo1.maven.org/maven2/org/apache/flink/flink-shaded-hadoop-2-uber/2.8.3-10.0/flink-shaded-hadoop-2-uber-2.8.3-10.0.jar"

echo "4. 下载S3文件系统支持..."
wget -O libs/flink-cdc/flink-s3-fs-hadoop-${FLINK_VERSION}.0.jar \
    "https://repo1.maven.org/maven2/org/apache/flink/flink-s3-fs-hadoop/${FLINK_VERSION}.0/flink-s3-fs-hadoop-${FLINK_VERSION}.0.jar"

echo "5. 下载MySQL JDBC驱动..."
wget -O libs/flink-cdc/mysql-connector-java-8.0.33.jar \
    "https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar"

echo "✓ 所有依赖下载完成"
echo "依赖文件位置: libs/flink-cdc/"
ls -la libs/flink-cdc/
