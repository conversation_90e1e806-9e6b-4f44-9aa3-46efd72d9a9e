#!/bin/bash
# 数据通路一键部署脚本
set -e
echo "=== 数据通路部署脚本 ==="
echo "MySQL → FlinkCDC → Paimon → Doris"
echo ""
# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color
# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 未安装，请先安装"
        exit 1
    fi
}
# 步骤1：环境检查
print_info "步骤1：检查环境..."
check_command docker
check_command docker-compose

# 检查Docker服务
if ! docker info &> /dev/null; then
    print_error "Docker服务未运行，请启动Docker服务"
    exit 1
fi
print_info "环境检查通过"
# 步骤2：创建目录结构
print_info "步骤2：创建目录结构..."
mkdir -p libs/flink-cdc
mkdir -p data/mysql
mkdir -p data/minio
mkdir -p data/flink
mkdir -p data/doris
# 步骤3：下载依赖（如果不存在）
print_info "步骤3：检查依赖文件..."
if [ ! -f "libs/flink-cdc/flink-cdc-mysql-2.4.2.jar" ]; then
    print_warn "依赖文件不存在，开始下载..."
    chmod +x scripts/download-dependencies.sh
    ./scripts/download-dependencies.sh
else
    print_info "依赖文件已存在"
fi

# 步骤4：启动服务
print_info "步骤4：启动Docker服务..."
docker-compose down 2>/dev/null || true
docker-compose up -d

# 步骤5：等待服务启动
print_info "步骤5：等待服务启动（这可能需要5-10分钟）..."
sleep 30

# 检查服务状态
print_info "检查服务状态..."
services=("mysql-source" "minio-storage" "flink-jobmanager" "flink-taskmanager" "doris-fe" "doris-be")

for service in "${services[@]}"; do
    if docker ps --format "table {{.Names}}" | grep -q "$service"; then
        print_info "✓ $service 运行中"
    else
        print_warn "✗ $service 可能未正常启动"
    fi
done

# 步骤6：初始化配置
print_info "步骤6：初始化配置..."

# 等待MySQL完全启动
print_info "等待MySQL完全启动..."
for i in {1..30}; do
    if docker exec mysql-source mysql -uroot -proot123 -e "SELECT 1" &>/dev/null; then
        print_info "MySQL已就绪"
        break
    fi
    sleep 2
done

# 创建MinIO bucket
print_info "配置MinIO存储桶..."
sleep 10
docker exec minio-storage mc config host add local http://localhost:9000 minioadmin minioadmin123 2>/dev/null || true
docker exec minio-storage mc mb local/paimon-warehouse --ignore-existing 2>/dev/null || true

# 步骤7：运行测试
print_info "步骤7：运行基础测试..."
chmod +x scripts/test-pipeline.sh
./scripts/test-pipeline.sh

# 完成信息
print_info "=== 部署完成 ==="
echo ""
echo "访问地址："
echo "- Flink Web UI: http://localhost:8081"
echo "- MinIO Console: http://localhost:9001 (minioadmin/minioadmin123)"
echo "- Doris FE: http://localhost:8030"
echo ""
echo "下一步操作："
echo "1. 访问Flink Web UI配置CDC作业"
echo "2. 连接Doris创建目标表: mysql -h localhost -P 9030 -u root"
echo "3. 查看详细文档: docs/deployment-guide.md"
echo ""
print_info "部署脚本执行完成！"
