#!/bin/bash

# 分步骤启动脚本
echo "========================================="
echo "    数据通路分步骤部署脚本"
echo "  MySQL → FlinkCDC → Paimon → Doris"
echo "========================================="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法: $0 [step]"
    echo ""
    echo "可用步骤:"
    echo "  1 - 启动MySQL"
    echo "  2 - 启动MinIO"
    echo "  3 - 启动Flink"
    echo "  4 - 启动Doris (可选)"
    echo "  all - 启动所有服务"
    echo "  status - 查看服务状态"
    echo "  logs - 查看日志"
    echo "  stop - 停止所有服务"
    echo ""
    exit 1
fi

STEP=$1

# 创建基础目录
setup_dirs() {
    log "创建目录结构..."
    mkdir -p {libs/flink-cdc,data/{mysql,minio,flink},logs}
    log "✓ 目录创建完成"
}

# 启动MySQL
start_mysql() {
    log "启动MySQL服务..."
    docker-compose -f docker-compose-china.yml up -d mysql
    
    log "等待MySQL启动..."
    for i in {1..30}; do
        if docker exec mysql-source mysql -uroot -proot123 -e "SELECT 1" &>/dev/null; then
            log "✓ MySQL启动成功"
            return 0
        fi
        echo -n "."
        sleep 2
    done
    error "MySQL启动超时"
    return 1
}

# 启动MinIO
start_minio() {
    log "启动MinIO服务..."
    docker-compose -f docker-compose-china.yml up -d minio
    
    log "等待MinIO启动..."
    for i in {1..20}; do
        if curl -s http://localhost:9000/minio/health/live >/dev/null; then
            log "✓ MinIO启动成功"
            return 0
        fi
        echo -n "."
        sleep 3
    done
    error "MinIO启动超时"
    return 1
}

# 启动Flink
start_flink() {
    log "启动Flink服务..."
    docker-compose -f docker-compose-china.yml up -d flink-jobmanager flink-taskmanager
    
    log "等待Flink启动..."
    for i in {1..30}; do
        if curl -s http://localhost:8081/overview >/dev/null; then
            log "✓ Flink启动成功"
            return 0
        fi
        echo -n "."
        sleep 3
    done
    error "Flink启动超时"
    return 1
}

# 查看服务状态
show_status() {
    log "查看服务状态..."
    docker-compose -f docker-compose-china.yml ps
    
    echo ""
    log "端口检查:"
    
    # 检查MySQL
    if nc -z localhost 3306 2>/dev/null; then
        echo "✓ MySQL (3306) - 运行中"
    else
        echo "✗ MySQL (3306) - 未运行"
    fi
    
    # 检查MinIO
    if nc -z localhost 9000 2>/dev/null; then
        echo "✓ MinIO (9000) - 运行中"
    else
        echo "✗ MinIO (9000) - 未运行"
    fi
    
    # 检查Flink
    if nc -z localhost 8081 2>/dev/null; then
        echo "✓ Flink (8081) - 运行中"
    else
        echo "✗ Flink (8081) - 未运行"
    fi
}

# 查看日志
show_logs() {
    log "查看服务日志..."
    docker-compose -f docker-compose-china.yml logs --tail=50 -f
}

# 停止服务
stop_services() {
    log "停止所有服务..."
    docker-compose -f docker-compose-china.yml down
    log "✓ 服务已停止"
}

# 主逻辑
case $STEP in
    "1")
        setup_dirs
        start_mysql
        ;;
    "2")
        start_minio
        ;;
    "3")
        start_flink
        ;;
    "all")
        setup_dirs
        start_mysql
        if [ $? -eq 0 ]; then
            start_minio
            if [ $? -eq 0 ]; then
                start_flink
            fi
        fi
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "stop")
        stop_services
        ;;
    *)
        error "未知步骤: $STEP"
        exit 1
        ;;
esac

if [ "$STEP" != "logs" ] && [ "$STEP" != "stop" ]; then
    echo ""
    echo "========================================="
    echo "下一步操作:"
    echo "  查看状态: $0 status"
    echo "  查看日志: $0 logs"
    echo "  停止服务: $0 stop"
    echo "========================================="
fi
