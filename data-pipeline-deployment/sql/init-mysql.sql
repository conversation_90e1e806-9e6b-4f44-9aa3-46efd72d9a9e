-- init-mysql.sql

-- 创建数据库
CREATE DATABASE IF NOT EXISTS test_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 切换到新创建的数据库
USE test_db;

-- 创建测试表：users
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- 创建测试表：products
CREATE TABLE IF NOT EXISTS products (
    product_id INT AUTO_INCREMENT PRIMARY KEY,
    product_name VARCHAR(255) NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    stock_quantity INT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB;

-- 插入初始数据到users表
INSERT INTO users (name, email) VALUES
('Alice', '<EMAIL>'),
('Bob', '<EMAIL>'),
('Charlie', '<EMAIL>');

-- 插入初始数据到products表
INSERT INTO products (product_name, price, stock_quantity) VALUES
('Laptop', 1200.50, 50),
('Smartphone', 800.00, 150),
('Keyboard', 75.99, 300);

-- 创建Flink CDC所需的用户并授权
CREATE USER 'flink_user'@'%' IDENTIFIED BY 'flink123';

-- 授予用户必要的权限
GRANT SELECT, RELOAD, SHOW DATABASES, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'flink_user'@'%';

-- 刷新权限使之生效
FLUSH PRIVILEGES;

