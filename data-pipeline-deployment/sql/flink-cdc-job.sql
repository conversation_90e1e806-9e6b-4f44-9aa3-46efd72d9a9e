-- Flink CDC作业SQL配置
-- 用于创建MySQL到Paimon的数据同步作业

-- 设置执行环境
SET execution.checkpointing.interval = 60s;
SET execution.checkpointing.timeout = 10min;
SET execution.checkpointing.min-pause = 5s;

-- 创建MySQL CDC源表 - users
CREATE TABLE mysql_users (
    id INT,
    name STRING,
    email STRING,
    created_at TIMESTAMP(3),
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'mysql-cdc',
    'hostname' = 'mysql-source',
    'port' = '3306',
    'username' = 'flink_user',
    'password' = 'flink123',
    'database-name' = 'test_db',
    'table-name' = 'users',
    'server-time-zone' = 'Asia/Shanghai'
);

-- 创建MySQL CDC源表 - products
CREATE TABLE mysql_products (
    product_id INT,
    product_name STRING,
    price DECIMAL(10, 2),
    stock_quantity INT,
    last_updated TIMESTAMP(3),
    PRIMARY KEY (product_id) NOT ENFORCED
) WITH (
    'connector' = 'mysql-cdc',
    'hostname' = 'mysql-source',
    'port' = '3306',
    'username' = 'flink_user',
    'password' = 'flink123',
    'database-name' = 'test_db',
    'table-name' = 'products',
    'server-time-zone' = 'Asia/Shanghai'
);

-- 创建Paimon目标表 - users
CREATE TABLE paimon_users (
    id INT,
    name STRING,
    email STRING,
    created_at TIMESTAMP(3),
    PRIMARY KEY (id) NOT ENFORCED
) WITH (
    'connector' = 'paimon',
    'path' = 's3://paimon-warehouse/users',
    's3.endpoint' = 'http://minio-storage:9000',
    's3.access-key' = 'minioadmin',
    's3.secret-key' = 'minioadmin123',
    's3.path-style-access' = 'true'
);

-- 创建Paimon目标表 - products
CREATE TABLE paimon_products (
    product_id INT,
    product_name STRING,
    price DECIMAL(10, 2),
    stock_quantity INT,
    last_updated TIMESTAMP(3),
    PRIMARY KEY (product_id) NOT ENFORCED
) WITH (
    'connector' = 'paimon',
    'path' = 's3://paimon-warehouse/products',
    's3.endpoint' = 'http://minio-storage:9000',
    's3.access-key' = 'minioadmin',
    's3.secret-key' = 'minioadmin123',
    's3.path-style-access' = 'true'
);

-- 创建数据同步作业 - users表
INSERT INTO paimon_users
SELECT id, name, email, created_at
FROM mysql_users;

-- 创建数据同步作业 - products表
INSERT INTO paimon_products
SELECT product_id, product_name, price, stock_quantity, last_updated
FROM mysql_products;
