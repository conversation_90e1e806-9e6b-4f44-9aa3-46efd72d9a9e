-- <PERSON>数据库初始化和配置脚本

-- 连接到Doris FE
-- mysql -h localhost -P 9030 -u root

-- 创建数据库
CREATE DATABASE IF NOT EXISTS lakehouse_db;
USE lakehouse_db;

-- 创建users表（从Paimon同步数据）
CREATE TABLE users (
    id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    created_at DATETIME
) DUPLICATE KEY(id)
DISTRIBUTED BY HASH(id) BUCKETS 10
PROPERTIES (
    "replication_num" = "1"
);

-- 创建products表（从Paimon同步数据）
CREATE TABLE products (
    product_id INT NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    price DECIMAL(10, 2) NOT NULL,
    stock_quantity INT,
    last_updated DATETIME
) DUPLICATE KEY(product_id)
DISTRIBUTED BY HASH(product_id) BUCKETS 10
PROPERTIES (
    "replication_num" = "1"
);

-- 创建用于数据分析的聚合表
CREATE TABLE user_product_summary (
    date_key DATE NOT NULL,
    total_users INT,
    total_products INT,
    avg_product_price DECIMAL(10, 2)
) AGGREGATE KEY(date_key)
DISTRIBUTED BY HASH(date_key) BUCKETS 5
PROPERTIES (
    "replication_num" = "1"
);

-- 查看表结构
SHOW TABLES;
DESC users;
DESC products;
