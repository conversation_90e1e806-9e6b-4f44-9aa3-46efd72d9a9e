# 数据通路架构图

这是一个使用Mermaid绘制的架构图，展示了从MySQL到Doris的完整数据流。

```mermaid
graph TD
    subgraph "数据源 (Source)"
        A[MySQL Database]
    end

    subgraph "数据捕获 (CDC)"
        B[Flink CDC Job]
    end

    subgraph "数据湖存储 (Data Lake)"
        C[Apache Paimon]
        D[MinIO S3 Storage]
    end

    subgraph "数据仓库 (Data Warehouse)"
        E[Apache Doris]
    end

    subgraph "数据分析与应用 (Analytics)"
        F[BI Tools / Dashboards]
    end

    %% 定义数据流
    A -- Binlog --> B
    B -- Real-time Sync --> C
    C -- Stores Data in --> D
    C -- Batch/Stream Load --> E
    E --> F

    %% 样式定义
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#bbf,stroke:#333,stroke-width:2px
    style C fill:#ccf,stroke:#333,stroke-width:2px
    style D fill:#99f,stroke:#333,stroke-width:2px
    style E fill:#f96,stroke:#333,stroke-width:2px
    style F fill:#9c9,stroke:#333,stroke-width:2px
```

## 架构说明

1.  **MySQL**: 作为业务数据库，所有的数据变更都会被记录在 `binlog` 中。
2.  **Flink CDC**: Flink作业通过CDC连接器实时捕获MySQL的 `binlog` 数据，进行解析和处理。
3.  **Apache Paimon**: Flink将处理后的数据实时写入Paimon表。Paimon作为流式数据湖，提供了高效的upsert能力和快照管理。
4.  **MinIO**: Paimon使用MinIO作为其底层对象存储，负责持久化数据文件。
5.  **Apache Doris**: Doris通过Paimon的外部表功能，或者通过例行导入任务，将数据从数据湖加载到其内部，用于高性能的在线分析查询（OLAP）。
6.  **BI工具**: 最终用户通过BI工具或仪表盘连接到Doris，进行数据分析和可视化。
