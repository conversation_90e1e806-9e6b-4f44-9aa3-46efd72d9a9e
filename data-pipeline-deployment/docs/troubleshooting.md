# 故障排除指南

## 常见问题及解决方案

### 1. MySQL相关问题

#### 问题：MySQL容器启动失败
**症状：** `docker-compose ps` 显示mysql-source状态为Exit
**解决方案：**
```bash
# 查看详细错误日志
docker-compose logs mysql

# 清理数据卷重新启动
docker-compose down -v
docker-compose up -d mysql
```

#### 问题：Flink无法连接MySQL
**症状：** CDC作业失败，提示连接被拒绝
**解决方案：**
```bash
# 检查MySQL用户权限
docker exec mysql-source mysql -uroot -proot123 -e "
SELECT User, Host FROM mysql.user WHERE User='flink_user';
SHOW GRANTS FOR 'flink_user'@'%';
"

# 重新创建用户（如果需要）
docker exec mysql-source mysql -uroot -proot123 -e "
DROP USER IF EXISTS 'flink_user'@'%';
CREATE USER 'flink_user'@'%' IDENTIFIED BY 'flink123';
GRANT SELECT, RELOAD, SHOW DATABASES, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'flink_user'@'%';
FLUSH PRIVILEGES;
"
```

#### 问题：Binlog未开启
**症状：** CDC无法捕获变更
**解决方案：**
```bash
# 检查binlog状态
docker exec mysql-source mysql -uroot -proot123 -e "SHOW VARIABLES LIKE 'log_bin';"

# 如果未开启，检查配置文件
cat configs/mysql/my.cnf
```

### 2. Flink相关问题

#### 问题：Flink JobManager内存不足
**症状：** JobManager频繁重启，OOM错误
**解决方案：**
```yaml
# 修改docker-compose.yml中的环境变量
environment:
  - JOB_MANAGER_RPC_ADDRESS=flink-jobmanager
  - FLINK_PROPERTIES=jobmanager.memory.process.size: 2048m
```

#### 问题：TaskManager连接失败
**症状：** Web UI显示无可用TaskManager
**解决方案：**
```bash
# 检查TaskManager日志
docker-compose logs flink-taskmanager

# 重启TaskManager
docker-compose restart flink-taskmanager

# 检查网络连通性
docker exec flink-jobmanager ping flink-taskmanager
```

#### 问题：CDC连接器未找到
**症状：** 提交作业时提示connector不存在
**解决方案：**
```bash
# 检查JAR文件是否存在
ls -la libs/flink-cdc/

# 重新下载依赖
./scripts/download-dependencies.sh

# 重启Flink服务
docker-compose restart flink-jobmanager flink-taskmanager
```

### 3. MinIO相关问题

#### 问题：MinIO访问被拒绝
**症状：** Paimon无法写入数据到MinIO
**解决方案：**
```bash
# 检查MinIO状态
curl http://localhost:9000/minio/health/live

# 重新配置访问密钥
docker exec minio-storage mc alias set local http://localhost:9000 minioadmin minioadmin123

# 检查bucket权限
docker exec minio-storage mc ls local/
```

#### 问题：存储空间不足
**症状：** 写入失败，磁盘空间错误
**解决方案：**
```bash
# 检查磁盘使用情况
df -h
docker system df

# 清理无用的Docker资源
docker system prune -f
```

### 4. Doris相关问题

#### 问题：Doris FE启动缓慢
**症状：** FE服务长时间处于启动状态
**解决方案：**
```bash
# 增加启动等待时间
sleep 120

# 检查内存配置
docker stats doris-fe

# 查看启动日志
docker-compose logs doris-fe
```

#### 问题：BE无法连接FE
**症状：** BE节点无法注册到FE
**解决方案：**
```bash
# 检查网络连通性
docker exec doris-be ping doris-fe

# 手动添加BE节点
docker exec doris-fe mysql -uroot -P9030 -h127.0.0.1 -e "
ALTER SYSTEM ADD BACKEND 'doris-be:9050';
"
```

### 5. 网络相关问题

#### 问题：容器间网络不通
**症状：** 服务间无法相互访问
**解决方案：**
```bash
# 检查Docker网络
docker network ls
docker network inspect data-pipeline-deployment_data-pipeline

# 重建网络
docker-compose down
docker-compose up -d
```

#### 问题：端口冲突
**症状：** 服务启动失败，端口被占用
**解决方案：**
```bash
# 检查端口占用
netstat -tulpn | grep :3306
netstat -tulpn | grep :8081

# 修改docker-compose.yml中的端口映射
# 或停止占用端口的服务
```

### 6. 性能问题

#### 问题：数据同步延迟高
**症状：** CDC数据延迟明显
**解决方案：**
```bash
# 调整检查点间隔
# 在Flink作业中设置更短的检查点间隔
SET execution.checkpointing.interval = 30s;

# 增加并行度
SET parallelism.default = 4;
```

#### 问题：内存使用过高
**症状：** 系统响应缓慢，内存不足
**解决方案：**
```bash
# 监控资源使用
docker stats

# 调整JVM参数
# 修改各服务的内存配置
```

## 日志查看命令

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs mysql
docker-compose logs flink-jobmanager
docker-compose logs flink-taskmanager
docker-compose logs minio
docker-compose logs doris-fe
docker-compose logs doris-be

# 实时查看日志
docker-compose logs -f flink-jobmanager

# 查看最近的日志
docker-compose logs --tail=100 mysql
```

## 健康检查脚本

```bash
# 创建健康检查脚本
cat > health-check.sh << 'EOF'
#!/bin/bash
echo "=== 服务健康检查 ==="

# MySQL
mysql_status=$(docker exec mysql-source mysql -uroot -proot123 -e "SELECT 1" 2>/dev/null && echo "OK" || echo "FAIL")
echo "MySQL: $mysql_status"

# Flink
flink_status=$(curl -s http://localhost:8081/overview >/dev/null && echo "OK" || echo "FAIL")
echo "Flink: $flink_status"

# MinIO
minio_status=$(curl -s http://localhost:9000/minio/health/live >/dev/null && echo "OK" || echo "FAIL")
echo "MinIO: $minio_status"

# Doris
doris_status=$(curl -s http://localhost:8030/api/bootstrap >/dev/null && echo "OK" || echo "FAIL")
echo "Doris: $doris_status"
EOF

chmod +x health-check.sh
```
