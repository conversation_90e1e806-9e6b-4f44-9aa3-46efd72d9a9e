# 数据通路部署完整指南

## 部署步骤

### 1. 环境准备

```bash
# 克隆或下载项目文件
cd data-pipeline-deployment

# 检查环境
chmod +x scripts/*.sh
./scripts/check-environment.sh

# 下载依赖包
./scripts/download-dependencies.sh
```

### 2. 启动服务

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 3. 验证服务启动

等待5-10分钟让所有服务完全启动，然后运行测试：

```bash
./scripts/test-pipeline.sh
```

### 4. 配置Flink CDC作业

#### 方法1：通过Flink Web UI
1. 访问 http://localhost:8081
2. 点击 "Submit New Job"
3. 上传包含CDC连接器的JAR文件
4. 提交作业

#### 方法2：通过SQL Client
```bash
# 进入Flink容器
docker exec -it flink-jobmanager bash

# 启动SQL Client
./bin/sql-client.sh

# 执行CDC配置SQL
```

### 5. 配置Doris数据库

```bash
# 连接到Doris
mysql -h localhost -P 9030 -u root

# 执行初始化脚本
source sql/doris-setup.sql
```

## 数据流验证

### 1. 在MySQL中插入数据
```sql
USE test_db;
INSERT INTO users (name, email) VALUES ('NewUser', '<EMAIL>');
INSERT INTO products (product_name, price, stock_quantity) VALUES ('NewProduct', 199.99, 50);
```

### 2. 检查Paimon存储
访问MinIO控制台：http://localhost:9001
- 用户名：minioadmin
- 密码：minioadmin123

### 3. 验证Doris中的数据
```sql
USE lakehouse_db;
SELECT * FROM users;
SELECT * FROM products;
```

## 监控和管理

### Web界面
- Flink JobManager: http://localhost:8081
- MinIO Console: http://localhost:9001
- Doris FE: http://localhost:8030

### 命令行监控
```bash
# 查看容器状态
docker-compose ps

# 查看特定服务日志
docker-compose logs mysql
docker-compose logs flink-jobmanager
docker-compose logs doris-fe

# 查看资源使用
docker stats
```

## 故障排除

### 常见问题

1. **MySQL连接失败**
   - 检查binlog配置
   - 验证用户权限
   - 确认网络连通性

2. **Flink作业失败**
   - 检查依赖JAR文件
   - 验证连接器配置
   - 查看TaskManager日志

3. **MinIO连接问题**
   - 确认访问密钥配置
   - 检查网络策略
   - 验证存储桶权限

4. **Doris启动慢**
   - 增加内存配置
   - 等待更长时间
   - 检查磁盘空间

### 日志位置
- MySQL: `/var/lib/mysql/`
- Flink: `/opt/flink/log/`
- Doris FE: `/opt/apache-doris/fe/log/`
- Doris BE: `/opt/apache-doris/be/log/`

## 性能优化

### 生产环境建议
1. 调整JVM内存配置
2. 配置检查点策略
3. 优化并行度设置
4. 配置高可用模式
5. 设置监控告警

### 扩容方案
- 增加Flink TaskManager节点
- 扩展Doris BE节点
- 配置MinIO集群模式
