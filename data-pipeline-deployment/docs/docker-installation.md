# Docker 和 Docker Compose 安装教程

## Ubuntu/Debian 系统安装

### 方法一：使用官方安装脚本（推荐）

```bash
# 下载并执行官方安装脚本
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 将当前用户添加到docker组（避免每次使用sudo）
sudo usermod -aG docker $USER

# 重新登录或执行以下命令使组权限生效
newgrp docker

# 设置Docker开机自启
sudo systemctl enable docker
sudo systemctl start docker
```

### 方法二：手动安装

```bash
# 更新包索引
sudo apt-get update

# 安装必要的包
sudo apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release

# 添加Docker官方GPG密钥
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

# 添加Docker仓库
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 更新包索引
sudo apt-get update

# 安装Docker Engine
sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 将用户添加到docker组
sudo usermod -aG docker $USER

# 启动Docker服务
sudo systemctl enable docker
sudo systemctl start docker
```

## CentOS/RHEL/Rocky Linux 系统安装

### 使用官方脚本

```bash
# 下载并执行官方安装脚本
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 将当前用户添加到docker组
sudo usermod -aG docker $USER

# 启动Docker服务
sudo systemctl enable docker
sudo systemctl start docker

# 重新登录使组权限生效
newgrp docker
```

### 手动安装

```bash
# 安装必要的包
sudo yum install -y yum-utils

# 添加Docker仓库
sudo yum-config-manager \
    --add-repo \
    https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker Engine
sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动Docker服务
sudo systemctl enable docker
sudo systemctl start docker

# 将用户添加到docker组
sudo usermod -aG docker $USER
newgrp docker
```

## Docker Compose 安装

### 方法一：使用Docker Compose插件（推荐）

现代版本的Docker已经包含了Compose插件，使用方式：

```bash
# 检查是否已安装
docker compose version

# 如果已安装，会显示版本信息
```

### 方法二：独立安装Docker Compose

```bash
# 下载最新版本的Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# 添加执行权限
sudo chmod +x /usr/local/bin/docker-compose

# 创建软链接（可选）
sudo ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose

# 验证安装
docker-compose --version
```

## 验证安装

```bash
# 检查Docker版本
docker --version

# 检查Docker服务状态
sudo systemctl status docker

# 运行测试容器
docker run hello-world

# 检查Docker Compose版本
docker compose version
# 或者（如果使用独立版本）
docker-compose --version
```

## 国内镜像加速配置

由于网络原因，建议配置国内镜像加速器：

```bash
# 创建或编辑Docker配置文件
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json <<-'EOF'
{
  "registry-mirrors": [
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  }
}
EOF

# 重启Docker服务
sudo systemctl daemon-reload
sudo systemctl restart docker
```

## 常见问题解决

### 1. 权限问题

```bash
# 如果出现权限错误，确保用户在docker组中
sudo usermod -aG docker $USER

# 重新登录或执行
newgrp docker
```

### 2. 服务启动失败

```bash
# 检查服务状态
sudo systemctl status docker

# 查看详细日志
sudo journalctl -u docker.service

# 重启服务
sudo systemctl restart docker
```

### 3. 磁盘空间不足

```bash
# 清理无用的镜像和容器
docker system prune -a

# 查看磁盘使用情况
docker system df
```

## 卸载Docker（如果需要）

### Ubuntu/Debian

```bash
# 停止Docker服务
sudo systemctl stop docker

# 卸载Docker包
sudo apt-get purge -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 删除Docker数据目录
sudo rm -rf /var/lib/docker
sudo rm -rf /var/lib/containerd
```

### CentOS/RHEL

```bash
# 停止Docker服务
sudo systemctl stop docker

# 卸载Docker包
sudo yum remove -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 删除Docker数据目录
sudo rm -rf /var/lib/docker
sudo rm -rf /var/lib/containerd
```

安装完成后，你就可以使用我们之前创建的数据通路部署方案了！
