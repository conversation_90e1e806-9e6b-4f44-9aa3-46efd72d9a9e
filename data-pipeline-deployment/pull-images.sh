#!/bin/bash

# 手动拉取Docker镜像脚本
echo "=== 手动拉取Docker镜像 ==="

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 定义镜像列表
declare -A IMAGES
IMAGES[mysql]="mysql:8.0"
IMAGES[minio]="minio/minio:latest"
IMAGES[flink]="flink:1.18.0-scala_2.12-java11"

# 定义国内镜像源
MIRRORS=(
    "registry.cn-hangzhou.aliyuncs.com/library"
    "hub-mirror.c.163.com/library"
    "dockerproxy.com/library"
)

# 拉取镜像函数
pull_image() {
    local image_name=$1
    local image_tag=$2
    
    log "拉取镜像: $image_name:$image_tag"
    
    # 首先尝试直接拉取
    if docker pull "$image_name:$image_tag" 2>/dev/null; then
        log "✓ 直接拉取成功: $image_name:$image_tag"
        return 0
    fi
    
    # 尝试国内镜像源
    for mirror in "${MIRRORS[@]}"; do
        log "尝试镜像源: $mirror"
        if docker pull "$mirror/$image_name:$image_tag" 2>/dev/null; then
            log "✓ 镜像源拉取成功: $mirror/$image_name:$image_tag"
            # 重新标记为原始名称
            docker tag "$mirror/$image_name:$image_tag" "$image_name:$image_tag"
            docker rmi "$mirror/$image_name:$image_tag" 2>/dev/null || true
            return 0
        fi
    done
    
    error "✗ 拉取失败: $image_name:$image_tag"
    return 1
}

# 主执行逻辑
main() {
    log "开始拉取所需镜像..."
    
    failed_images=()
    
    for key in "${!IMAGES[@]}"; do
        image="${IMAGES[$key]}"
        if ! pull_image "${image%:*}" "${image#*:}"; then
            failed_images+=("$image")
        fi
        echo ""
    done
    
    # 显示结果
    echo "========================================="
    if [ ${#failed_images[@]} -eq 0 ]; then
        log "✓ 所有镜像拉取成功！"
        echo ""
        log "已拉取的镜像:"
        docker images | grep -E "(mysql|minio|flink)" | head -10
    else
        warn "以下镜像拉取失败:"
        for img in "${failed_images[@]}"; do
            echo "  - $img"
        done
        echo ""
        warn "请检查网络连接或手动拉取失败的镜像"
    fi
    echo "========================================="
}

# 如果提供了参数，只拉取指定镜像
if [ $# -gt 0 ]; then
    case $1 in
        "mysql")
            pull_image "mysql" "8.0"
            ;;
        "minio")
            pull_image "minio/minio" "latest"
            ;;
        "flink")
            pull_image "flink" "1.18.0-scala_2.12-java11"
            ;;
        *)
            echo "用法: $0 [mysql|minio|flink]"
            echo "不提供参数则拉取所有镜像"
            exit 1
            ;;
    esac
else
    main
fi
