# 数据通路部署方案：MySQL → FlinkCDC → Paimon → Doris

## 架构概述

这是一个现代化的实时数据处理架构，包含以下组件：

1. **MySQL** - 源数据库，开启binlog进行CDC
2. **Apache Flink + FlinkCDC** - 实时数据捕获和处理
3. **Apache Paimon** - 数据湖存储层
4. **Apache Doris** - 分析型数据库
5. **MinIO** - 对象存储（作为Paimon底层存储）

## 架构流程

```
MySQL (binlog) → FlinkCDC → Paimon (数据湖) → Doris (分析查询)
```

## 部署环境要求

- Linux服务器（推荐8GB+ RAM，4+ CPU cores）
- Docker & Docker Compose
- 至少50GB可用磁盘空间

## 快速开始

1. 克隆项目并进入目录
2. 执行环境检查脚本：`./scripts/check-environment.sh`
3. 启动所有服务：`docker-compose up -d`
4. 运行测试脚本：`./scripts/test-pipeline.sh`

## 目录结构

```
data-pipeline-deployment/
├── docker-compose.yml          # 主要的Docker Compose配置
├── configs/                    # 各组件配置文件
│   ├── mysql/
│   ├── flink/
│   ├── paimon/
│   └── doris/
├── scripts/                    # 部署和测试脚本
├── sql/                       # SQL脚本和示例
└── docs/                      # 详细文档
```

## 组件端口映射

- MySQL: 3306
- Flink JobManager: 8081
- Flink TaskManager: 8082
- MinIO: 9000 (API), 9001 (Console)
- Doris FE: 8030, 9030
- Doris BE: 8040, 9060

## 监控和管理

- Flink Web UI: http://localhost:8081
- MinIO Console: http://localhost:9001
- Doris FE UI: http://localhost:8030

## 注意事项

1. 首次启动需要等待所有服务完全启动（约5-10分钟）
2. 确保防火墙开放相应端口
3. 生产环境建议调整JVM内存配置
4. 定期备份重要数据

## 故障排除

详见 `docs/troubleshooting.md`
