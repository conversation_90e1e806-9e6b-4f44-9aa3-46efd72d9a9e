#!/bin/bash

# 简化的依赖下载脚本
echo "=== 下载必要的JAR文件 ==="

# 创建目录
mkdir -p libs/flink-cdc

# 设置变量
FLINK_CDC_VERSION="2.4.2"
FLINK_VERSION="1.18"

# 下载函数
download_with_fallback() {
    local filename=$1
    local url1=$2
    local url2=$3
    local description=$4
    
    echo "正在下载 $description..."
    
    # 检查文件是否已存在
    if [ -f "libs/flink-cdc/$filename" ]; then
        echo "✓ $filename 已存在，跳过下载"
        return 0
    fi
    
    # 尝试第一个URL
    if wget -q --timeout=30 -O "libs/flink-cdc/$filename" "$url1"; then
        echo "✓ $description 下载成功"
        return 0
    fi
    
    # 尝试第二个URL（如果提供）
    if [ -n "$url2" ] && wget -q --timeout=30 -O "libs/flink-cdc/$filename" "$url2"; then
        echo "✓ $description 下载成功 (备用源)"
        return 0
    fi
    
    # 尝试curl
    if curl -L --connect-timeout 30 -o "libs/flink-cdc/$filename" "$url1" 2>/dev/null; then
        echo "✓ $description 下载成功 (curl)"
        return 0
    fi
    
    echo "✗ $description 下载失败"
    return 1
}

# 下载Flink CDC MySQL连接器
download_with_fallback \
    "flink-cdc-mysql-${FLINK_CDC_VERSION}.jar" \
    "https://repo1.maven.org/maven2/com/ververica/flink-cdc-mysql/${FLINK_CDC_VERSION}/flink-cdc-mysql-${FLINK_CDC_VERSION}.jar" \
    "https://maven.aliyun.com/repository/public/com/ververica/flink-cdc-mysql/${FLINK_CDC_VERSION}/flink-cdc-mysql-${FLINK_CDC_VERSION}.jar" \
    "Flink CDC MySQL连接器"

# 下载MySQL JDBC驱动
download_with_fallback \
    "mysql-connector-java-8.0.33.jar" \
    "https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar" \
    "https://maven.aliyun.com/repository/public/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar" \
    "MySQL JDBC驱动"

# 下载Paimon连接器
download_with_fallback \
    "paimon-flink-${FLINK_VERSION}-0.6.0.jar" \
    "https://repo1.maven.org/maven2/org/apache/paimon/paimon-flink-${FLINK_VERSION}/0.6.0/paimon-flink-${FLINK_VERSION}-0.6.0.jar" \
    "" \
    "Paimon Flink连接器"

# 下载S3文件系统支持
download_with_fallback \
    "flink-s3-fs-hadoop-${FLINK_VERSION}.0.jar" \
    "https://repo1.maven.org/maven2/org/apache/flink/flink-s3-fs-hadoop/${FLINK_VERSION}.0/flink-s3-fs-hadoop-${FLINK_VERSION}.0.jar" \
    "" \
    "S3文件系统支持"

echo ""
echo "=== 下载完成 ==="
echo "检查下载的文件："
ls -la libs/flink-cdc/

# 检查关键文件
echo ""
echo "=== 文件检查 ==="
required_files=(
    "flink-cdc-mysql-${FLINK_CDC_VERSION}.jar"
    "mysql-connector-java-8.0.33.jar"
)

missing_files=()
for file in "${required_files[@]}"; do
    if [ -f "libs/flink-cdc/$file" ]; then
        echo "✓ $file"
    else
        echo "✗ $file (缺失)"
        missing_files+=("$file")
    fi
done

if [ ${#missing_files[@]} -eq 0 ]; then
    echo ""
    echo "✓ 所有必需文件下载完成！"
    exit 0
else
    echo ""
    echo "⚠ 以下文件下载失败，需要手动下载："
    for file in "${missing_files[@]}"; do
        echo "  - $file"
    done
    echo ""
    echo "请手动下载这些文件到 libs/flink-cdc/ 目录"
    exit 1
fi
