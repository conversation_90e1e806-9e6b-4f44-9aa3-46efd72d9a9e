#!/bin/bash

# Docker镜像源配置脚本
echo "=== 配置Docker镜像源 ==="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root权限运行此脚本: sudo $0"
    exit 1
fi

# 备份原配置
if [ -f /etc/docker/daemon.json ]; then
    cp /etc/docker/daemon.json /etc/docker/daemon.json.backup.$(date +%Y%m%d_%H%M%S)
    echo "✓ 已备份原配置文件"
fi

# 创建Docker配置目录
mkdir -p /etc/docker

# 配置国内镜像源
cat > /etc/docker/daemon.json << 'EOF'
{
  "registry-mirrors": [
    "https://registry.cn-hangzhou.aliyuncs.com",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com",
    "https://dockerproxy.com"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false,
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  }
}
EOF

echo "✓ Docker镜像源配置完成"

# 重启Docker服务
echo "重启Docker服务..."
systemctl daemon-reload
systemctl restart docker

# 检查Docker状态
if systemctl is-active --quiet docker; then
    echo "✓ Docker服务重启成功"
else
    echo "✗ Docker服务重启失败"
    exit 1
fi

# 验证配置
echo "验证镜像源配置..."
docker info | grep -A 10 "Registry Mirrors" || echo "配置验证完成"

echo ""
echo "=== 镜像源配置完成 ==="
echo "现在可以正常拉取Docker镜像了"
