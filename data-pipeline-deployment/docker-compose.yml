version: '3.3'

services:
  # MySQL数据库 - 数据源
  mysql:
    image: mysql:8.0
    container_name: mysql-source
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: test_db
      MYSQL_USER: flink_user
      MYSQL_PASSWORD: flink123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./configs/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./sql/init-mysql.sql:/docker-entrypoint-initdb.d/init.sql
    command: --server-id=1 --log-bin=mysql-bin --binlog-format=ROW --binlog-row-image=FULL
    networks:
      - data-pipeline

  # MinIO对象存储 - Paimon底层存储
  minio:
    image: minio/minio:latest
    container_name: minio-storage
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - data-pipeline

  # Flink JobManager
  flink-jobmanager:
    image: flink:1.18.0-scala_2.12-java11
    container_name: flink-jobmanager
    environment:
      - JOB_MANAGER_RPC_ADDRESS=flink-jobmanager
      - FLINK_PROPERTIES=jobmanager.rpc.address: flink-jobmanager
    ports:
      - "8081:8081"
    volumes:
      - ./configs/flink/flink-conf.yaml:/opt/flink/conf/flink-conf.yaml
      - ./libs/flink-cdc:/opt/flink/lib/flink-cdc
      - flink_checkpoints:/opt/flink/checkpoints
    command: jobmanager
    networks:
      - data-pipeline
    depends_on:
      - mysql
      - minio

  # Flink TaskManager
  flink-taskmanager:
    image: flink:1.18.0-scala_2.12-java11
    container_name: flink-taskmanager
    environment:
      - JOB_MANAGER_RPC_ADDRESS=flink-jobmanager
      - FLINK_PROPERTIES=jobmanager.rpc.address: flink-jobmanager
    ports:
      - "8082:8081"
    volumes:
      - ./configs/flink/flink-conf.yaml:/opt/flink/conf/flink-conf.yaml
      - ./libs/flink-cdc:/opt/flink/lib/flink-cdc
      - flink_checkpoints:/opt/flink/checkpoints
    command: taskmanager
    networks:
      - data-pipeline
    depends_on:
      - flink-jobmanager

  # Doris Frontend
  doris-fe:
    image: apache/doris:2.0.3-fe-x86_64
    container_name: doris-fe
    environment:
      - FE_SERVERS=fe1:doris-fe:9010
      - FE_ID=1
    ports:
      - "8030:8030"
      - "9030:9030"
    volumes:
      - doris_fe_data:/opt/apache-doris/fe/doris-meta
      - ./configs/doris/fe.conf:/opt/apache-doris/fe/conf/fe.conf
    networks:
      - data-pipeline

  # Doris Backend
  doris-be:
    image: apache/doris:2.0.3-be-x86_64
    container_name: doris-be
    environment:
      - FE_SERVERS=fe1:doris-fe:9010
      - BE_ADDR=doris-be:9050
    ports:
      - "8040:8040"
      - "9060:9060"
    volumes:
      - doris_be_data:/opt/apache-doris/be/storage
      - ./configs/doris/be.conf:/opt/apache-doris/be/conf/be.conf
    networks:
      - data-pipeline
    depends_on:
      - doris-fe

volumes:
  mysql_data:
  minio_data:
  flink_checkpoints:
  doris_fe_data:
  doris_be_data:

networks:
  data-pipeline:
    driver: bridge
