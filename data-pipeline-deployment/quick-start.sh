#!/bin/bash

# 快速启动脚本 - 一键部署数据通路
echo "========================================="
echo "    数据通路快速部署脚本"
echo "  MySQL → FlinkCDC → Paimon → Doris"
echo "========================================="

# 设置错误时退出
set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查Docker环境
check_docker() {
    log "检查Docker环境..."
    if ! command -v docker &> /dev/null; then
        error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log "✓ Docker环境检查通过"
}

# 清理旧环境
cleanup_old() {
    log "清理旧的部署环境..."
    docker-compose down -v 2>/dev/null || true
    docker system prune -f 2>/dev/null || true
    log "✓ 环境清理完成"
}

# 创建目录结构
setup_directories() {
    log "创建目录结构..."
    mkdir -p {libs/flink-cdc,data/{mysql,minio,flink,doris},logs}
    log "✓ 目录结构创建完成"
}

# 下载依赖
download_deps() {
    log "检查并下载依赖文件..."
    
    if [ ! -f "libs/flink-cdc/flink-cdc-mysql-2.4.2.jar" ]; then
        log "下载Flink CDC MySQL连接器..."
        wget -q -O libs/flink-cdc/flink-cdc-mysql-2.4.2.jar \
            "https://repo1.maven.org/maven2/com/ververica/flink-cdc-mysql/2.4.2/flink-cdc-mysql-2.4.2.jar" || \
            warn "下载失败，请手动下载到 libs/flink-cdc/ 目录"
    fi
    
    if [ ! -f "libs/flink-cdc/mysql-connector-java-8.0.33.jar" ]; then
        log "下载MySQL JDBC驱动..."
        wget -q -O libs/flink-cdc/mysql-connector-java-8.0.33.jar \
            "https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar" || \
            warn "下载失败，请手动下载到 libs/flink-cdc/ 目录"
    fi
    
    log "✓ 依赖检查完成"
}

# 启动服务
start_services() {
    log "启动Docker服务..."
    docker-compose up -d
    
    log "等待服务启动（预计需要3-5分钟）..."
    sleep 60
    
    # 检查服务状态
    log "检查服务状态..."
    services=("mysql-source" "minio-storage" "flink-jobmanager" "flink-taskmanager" "doris-fe" "doris-be")
    
    for service in "${services[@]}"; do
        if docker ps --format "table {{.Names}}" | grep -q "$service"; then
            log "✓ $service 运行正常"
        else
            warn "✗ $service 可能未正常启动"
        fi
    done
}

# 初始化配置
initialize_config() {
    log "初始化服务配置..."
    
    # 等待MySQL完全启动
    log "等待MySQL启动..."
    for i in {1..30}; do
        if docker exec mysql-source mysql -uroot -proot123 -e "SELECT 1" &>/dev/null; then
            log "✓ MySQL已就绪"
            break
        fi
        sleep 3
    done
    
    # 配置MinIO
    log "配置MinIO存储..."
    sleep 10
    docker exec minio-storage mc config host add local http://localhost:9000 minioadmin minioadmin123 2>/dev/null || true
    docker exec minio-storage mc mb local/paimon-warehouse --ignore-existing 2>/dev/null || true
    log "✓ MinIO配置完成"
    
    # 等待Doris启动
    log "等待Doris启动..."
    sleep 30
    log "✓ 服务初始化完成"
}

# 运行测试
run_tests() {
    log "运行基础连通性测试..."
    
    # 测试MySQL
    if docker exec mysql-source mysql -uroot -proot123 -e "SELECT 'MySQL连接成功' as status;" &>/dev/null; then
        log "✓ MySQL连接测试通过"
    else
        warn "✗ MySQL连接测试失败"
    fi
    
    # 测试Flink
    if curl -s http://localhost:8081/overview >/dev/null; then
        log "✓ Flink Web UI访问正常"
    else
        warn "✗ Flink Web UI访问失败"
    fi
    
    # 测试MinIO
    if curl -s http://localhost:9000/minio/health/live >/dev/null; then
        log "✓ MinIO服务正常"
    else
        warn "✗ MinIO服务异常"
    fi
    
    # 插入测试数据
    log "插入测试数据..."
    docker exec mysql-source mysql -uroot -proot123 test_db -e "
        INSERT INTO users (name, email) VALUES ('TestUser', '<EMAIL>');
        INSERT INTO products (product_name, price, stock_quantity) VALUES ('TestProduct', 99.99, 100);
    " 2>/dev/null || warn "测试数据插入失败"
    
    log "✓ 基础测试完成"
}

# 显示访问信息
show_access_info() {
    echo ""
    echo "========================================="
    echo "           部署完成！"
    echo "========================================="
    echo ""
    echo "🌐 Web访问地址："
    echo "  • Flink Web UI:    http://localhost:8081"
    echo "  • MinIO Console:    http://localhost:9001"
    echo "    用户名/密码:      minioadmin/minioadmin123"
    echo "  • Doris FE:         http://localhost:8030"
    echo ""
    echo "[object Object]            mysql -h localhost -P 3306 -u root -p"
    echo "    密码:             root123"
    echo "  • Doris:            mysql -h localhost -P 9030 -u root"
    echo ""
    echo "📋 下一步操作："
    echo "  1. 访问Flink Web UI配置CDC作业"
    echo "  2. 执行: ./scripts/submit-flink-job.sh"
    echo "  3. 连接Doris创建分析表"
    echo "  4. 查看详细文档: docs/deployment-guide.md"
    echo ""
    echo "[object Object] 查看服务状态:     docker-compose ps"
    echo "  • 查看日志:         docker-compose logs -f"
    echo "  • 停止服务:         docker-compose down"
    echo "  • 完全清理:         docker-compose down -v"
    echo ""
    echo "========================================="
}

# 主执行流程
main() {
    log "开始部署数据通路..."
    
    check_docker
    cleanup_old
    setup_directories
    download_deps
    start_services
    initialize_config
    run_tests
    show_access_info
    
    log "部署脚本执行完成！"
}

# 执行主函数
main "$@"
