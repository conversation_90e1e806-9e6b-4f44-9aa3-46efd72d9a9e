# Flink配置文件

# JobManager配置
jobmanager.rpc.address: flink-jobmanager
jobmanager.rpc.port: 6123
jobmanager.memory.process.size: 1600m
jobmanager.execution.failover-strategy: region

# TaskManager配置
taskmanager.numberOfTaskSlots: 4
taskmanager.memory.process.size: 2048m
taskmanager.memory.flink.size: 1280m

# 并行度配置
parallelism.default: 2

# 检查点配置
state.backend: rocksdb
state.checkpoints.dir: file:///opt/flink/checkpoints
state.savepoints.dir: file:///opt/flink/savepoints
execution.checkpointing.interval: 60000
execution.checkpointing.min-pause: 5000
execution.checkpointing.timeout: 600000
execution.checkpointing.max-concurrent-checkpoints: 1

# 重启策略
restart-strategy: fixed-delay
restart-strategy.fixed-delay.attempts: 3
restart-strategy.fixed-delay.delay: 10s

# Web UI配置
web.submit.enable: true
web.cancel.enable: true

# 高可用配置（可选）
# high-availability: zookeeper
# high-availability.zookeeper.quorum: localhost:2181

# 网络配置
taskmanager.network.memory.fraction: 0.1
taskmanager.network.memory.min: 64mb
taskmanager.network.memory.max: 1gb

# 日志配置
rootLogger.level: INFO
logger.akka.level: INFO
logger.kafka.level: INFO
logger.hadoop.level: WARN
logger.zookeeper.level: INFO

# 类加载器配置
classloader.resolve-order: child-first

# 时区配置
table.local-time-zone: Asia/Shanghai
