[mysqld]
# 服务器配置
server-id = 1
log-bin = mysql-bin
binlog-format = ROW
binlog-row-image = FULL
expire_logs_days = 7
max_binlog_size = 100M

# CDC相关配置
gtid-mode = ON
enforce-gtid-consistency = ON
log-slave-updates = ON

# 性能优化
innodb_buffer_pool_size = 256M
innodb_log_file_size = 64M
innodb_flush_log_at_trx_commit = 1
sync_binlog = 1

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 连接配置
max_connections = 200
max_connect_errors = 1000

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
