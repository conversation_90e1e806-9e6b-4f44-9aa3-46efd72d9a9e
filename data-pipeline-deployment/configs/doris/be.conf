# Doris Backend配置文件

# 基础配置
CUR_DATE = `date +%Y%m%d-%H%M%S`

# 数据目录
storage_root_path = /opt/apache-doris/be/storage
LOG_DIR = /opt/apache-doris/be/log

# 网络配置
priority_networks = 172.0.0.0/8;192.168.0.0/16;10.0.0.0/8
be_addr = 0.0.0.0

# 端口配置
be_port = 9060
webserver_port = 8040
heartbeat_service_port = 9050
brpc_port = 8060

# JVM配置
JAVA_OPTS = "-Xmx1024m -XX:+UseMembar -XX:SurvivorRatio=8 -XX:MaxTenuringThreshold=7 -XX:+PrintGCDateStamps -XX:+PrintGCDetails -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:+CMSClassUnloadingEnabled -XX:-CMSParallelRemarkEnabled -XX:CMSInitiatingOccupancyFraction=80 -XX:SoftRefLRUPolicyMSPerMB=0 -Xloggc:/opt/apache-doris/be/log/be.gc.log"

# 日志配置
LOG_LEVEL = INFO
sys_log_level = INFO

# 内存配置
mem_limit = 80%
write_buffer_size = 104857600
max_tablet_version_num = 500

# 存储配置
default_num_rows_per_column_file_block = 1024
disable_storage_page_cache = false
index_stream_cache_capacity = 10737418240

# 压缩配置
compress_rowbatches = true
storage_format_version = 2

# 查询配置
fragment_pool_thread_num_min = 64
fragment_pool_thread_num_max = 256
fragment_pool_queue_size = 2048

# 导入配置
push_worker_count_normal_priority = 3
push_worker_count_high_priority = 3
load_data_reserve_hours = 4
load_error_log_reserve_hours = 48

# 压缩配置
base_compaction_num_threads_per_disk = 1
cumulative_compaction_num_threads_per_disk = 1

# 网络配置
max_client_cache_size_per_host = 10
max_connection_pool_size = 16

# 其他配置
report_task_interval_seconds = 10
report_disk_state_interval_seconds = 60
report_tablet_interval_seconds = 60
report_workgroup_interval_seconds = 5
