# Doris Frontend配置文件

# 基础配置
CUR_DATE = `date +%Y%m%d-%H%M%S`

# 数据目录
meta_dir = /opt/apache-doris/fe/doris-meta
LOG_DIR = /opt/apache-doris/fe/log

# 网络配置
priority_networks = *********/8;***********/16;10.0.0.0/8
frontend_address = 0.0.0.0

# HTTP端口配置
http_port = 8030
rpc_port = 9020
query_port = 9030
edit_log_port = 9010

# JVM配置
JAVA_OPTS = "-Xmx2048m -XX:+UseMembar -XX:SurvivorRatio=8 -XX:MaxTenuringThreshold=7 -XX:+PrintGCDateStamps -XX:+PrintGCDetails -XX:+UseConcMarkSweepGC -XX:+UseParNewGC -XX:+CMSClassUnloadingEnabled -XX:-CMSParallelRemarkEnabled -XX:CMSInitiatingOccupancyFraction=80 -XX:SoftRefLRUPolicyMSPerMB=0 -Xloggc:/opt/apache-doris/fe/log/fe.gc.log"

# 日志配置
LOG_LEVEL = INFO
sys_log_level = INFO
sys_log_roll_mode = SIZE-MB-1024
sys_log_roll_num = 10

# 集群配置
cluster_id = -1
enable_fqdn_mode = false

# 元数据和事务配置
edit_log_roll_num = 50000
meta_delay_toleration_second = 300
master_sync_policy = SYNC
replica_sync_policy = SYNC

# 查询配置
qe_max_connection = 1024
max_conn_per_user = 100

# 存储配置
tablet_create_timeout_second = 1
max_create_table_timeout_second = 60
max_running_txn_num_per_db = 100

# 导入配置
max_load_timeout_second = 259200
min_load_timeout_second = 1

# 备份恢复配置
backup_job_default_timeout_ms = 86400000
